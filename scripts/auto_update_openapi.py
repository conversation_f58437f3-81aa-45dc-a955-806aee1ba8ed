#!/usr/bin/env python3
"""
OpenAPI Auto-Updater

This script automatically updates the OpenAPI specification by:
1. Scanning the codebase for missing endpoints
2. Generating OpenAPI definitions for missing endpoints
3. Appending them to the existing gpt.openapi.yaml file
"""

import sys
import re
import yaml
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class OpenAPIAutoUpdater:
    def __init__(self, openapi_file: str = "gpt.openapi.yaml"):
        self.openapi_file = project_root / openapi_file
        self.spec = None
        self.discovered_endpoints = {}
        self.existing_endpoints = set()
        
    def load_existing_spec(self) -> bool:
        """Load the existing OpenAPI specification."""
        try:
            with open(self.openapi_file, 'r', encoding='utf-8') as f:
                self.spec = yaml.safe_load(f)
            
            print(f"✅ Successfully loaded {self.openapi_file}")
            
            # Extract existing endpoints
            if "paths" in self.spec:
                for path, methods in self.spec["paths"].items():
                    for method in methods.keys():
                        self.existing_endpoints.add(f"{method.upper()} {path}")
                        
            print(f"📋 Found {len(self.existing_endpoints)} existing endpoints in spec")
            return True
            
        except Exception as e:
            print(f"❌ Error loading OpenAPI spec: {e}")
            return False
    
    def scan_fastapi_routes(self, file_path: Path) -> Dict[str, Dict]:
        """Scan a FastAPI route file for endpoint definitions."""
        endpoints = {}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # More comprehensive pattern to catch all router decorators
            # This pattern looks for @router.method('/path') followed by function definition
            pattern = r'@router\.(get|post|put|patch|delete)\(["\']([^"\']+)["\'][^)]*\)\s*(?:@[^\n]*\s*)*\s*async\s+def\s+(\w+)'

            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)

            for method, path, func_name in matches:
                endpoint_key = f"{method.upper()} {path}"

                # Extract docstring for this function
                func_pattern = rf'async\s+def\s+{re.escape(func_name)}\s*\([^)]*\):\s*"""([^"]*?)"""'
                docstring_match = re.search(func_pattern, content, re.DOTALL)
                docstring = docstring_match.group(1).strip() if docstring_match else ""

                # Extract summary from docstring or function name
                if docstring:
                    lines = docstring.split('\n')
                    summary = lines[0].strip() if lines else func_name.replace('_', ' ').title()
                    description = docstring
                else:
                    summary = func_name.replace('_', ' ').title()
                    description = f"Handle {method.upper()} request for {path}"

                # Determine tags based on path
                tags = self._determine_tags(path)

                # Check if this specific function requires auth by looking for @async_require_auth decorator
                func_start = content.find(f'@router.{method.lower()}(\'{path}\'')
                if func_start == -1:
                    func_start = content.find(f'@router.{method.lower()}("{path}"')

                requires_auth = False
                if func_start != -1:
                    # Look for the function definition after the router decorator
                    func_def_start = content.find(f'async def {func_name}', func_start)
                    if func_def_start != -1:
                        # Check the decorators between router and function
                        decorators_section = content[func_start:func_def_start]
                        requires_auth = '@async_require_auth' in decorators_section

                endpoints[endpoint_key] = {
                    'method': method.lower(),
                    'path': path,
                    'summary': summary,
                    'description': description,
                    'tags': tags,
                    'requires_auth': requires_auth,
                    'function_name': func_name
                }

        except Exception as e:
            print(f"⚠️  Error scanning {file_path}: {e}")

        return endpoints
    
    def _determine_tags(self, path: str) -> List[str]:
        """Determine appropriate tags for an endpoint based on its path."""
        if "earnings" in path:
            return ["Earnings Calendar"]
        elif "events" in path or "financial" in path:
            return ["Financial Calendar"]
        elif "sec" in path:
            return ["SEC Filings"]
        elif "user_query" in path or "ticker_facts" in path:
            return ["User Queries"]
        elif "share" in path:
            return ["Shared Queries"]
        elif "assistant" in path:
            return ["Assistant"]
        elif "cache" in path:
            return ["Cache"]
        elif "models" in path:
            return ["Models"]
        elif path == "/" or "healthcheck" in path:
            return ["Root"]
        else:
            return ["Conversation"]
    
    def discover_all_endpoints(self) -> Dict[str, Dict]:
        """Discover all API endpoints in the codebase with details."""
        all_endpoints = {}
        api_dir = project_root / "api"
        
        print("🔍 Scanning codebase for API endpoints...")
        
        # Scan FastAPI async route files
        for file_path in api_dir.glob("async_*.py"):
            endpoints = self.scan_fastapi_routes(file_path)
            all_endpoints.update(endpoints)
            if endpoints:
                print(f"  📁 {file_path.name}: {len(endpoints)} endpoints")
        
        # Scan main async app file
        async_app_path = project_root / "async_app.py"
        if async_app_path.exists():
            endpoints = self.scan_fastapi_routes(async_app_path)
            all_endpoints.update(endpoints)
            if endpoints:
                print(f"  📁 async_app.py: {len(endpoints)} endpoints")
        
        self.discovered_endpoints = all_endpoints
        print(f"📋 Total discovered endpoints: {len(all_endpoints)}")
        return all_endpoints
    
    def find_missing_endpoints(self) -> Dict[str, Dict]:
        """Find endpoints that are in the codebase but missing from the OpenAPI spec."""
        missing = {}
        
        for endpoint_key, endpoint_info in self.discovered_endpoints.items():
            if endpoint_key not in self.existing_endpoints:
                missing[endpoint_key] = endpoint_info
        
        return missing
    
    def generate_openapi_path(self, endpoint_info: Dict) -> Dict[str, Any]:
        """Generate OpenAPI path definition for an endpoint."""
        method = endpoint_info['method']
        path = endpoint_info['path']
        
        # Build basic operation
        operation = {
            "summary": endpoint_info['summary'],
            "description": endpoint_info['description'],
            "tags": endpoint_info['tags'],
            "responses": {
                "200": {
                    "description": "Successful response",
                    "content": {
                        "application/json": {
                            "schema": {"type": "object"}
                        }
                    }
                },
                "500": {
                    "description": "Server error",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                        }
                    }
                }
            }
        }
        
        # Add security if needed
        if endpoint_info['requires_auth']:
            operation["security"] = [{"Auth0": []}]
            operation["responses"]["401"] = {
                "description": "Authentication required",
                "content": {
                    "application/json": {
                        "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                    }
                }
            }
        
        # Add path parameters if present
        path_params = re.findall(r'\{([^}]+)\}', path)
        if path_params:
            operation["parameters"] = []
            for param in path_params:
                operation["parameters"].append({
                    "name": param,
                    "in": "path",
                    "required": True,
                    "schema": {"type": "string"},
                    "description": f"{param.replace('_', ' ').title()} identifier"
                })
        
        # Add request body for POST/PUT/PATCH methods
        if method in ['post', 'put', 'patch']:
            operation["requestBody"] = {
                "required": True,
                "content": {
                    "application/json": {
                        "schema": {"type": "object"}
                    }
                }
            }
        
        return {method: operation}
    
    def update_openapi_spec(self, missing_endpoints: Dict[str, Dict]) -> bool:
        """Update the OpenAPI specification with missing endpoints."""
        if not missing_endpoints:
            print("✅ No missing endpoints found. OpenAPI spec is up to date!")
            return True
        
        print(f"📝 Adding {len(missing_endpoints)} missing endpoints to OpenAPI spec...")
        
        # Ensure paths section exists
        if "paths" not in self.spec:
            self.spec["paths"] = {}
        
        # Add missing endpoints
        for endpoint_key, endpoint_info in missing_endpoints.items():
            path = endpoint_info['path']
            path_def = self.generate_openapi_path(endpoint_info)
            
            if path not in self.spec["paths"]:
                self.spec["paths"][path] = {}
            
            self.spec["paths"][path].update(path_def)
            print(f"  ➕ Added: {endpoint_key}")
        
        # Ensure required tags exist
        existing_tag_names = {tag['name'] for tag in self.spec.get('tags', [])}
        required_tags = set()
        for endpoint_info in missing_endpoints.values():
            required_tags.update(endpoint_info['tags'])
        
        new_tags = required_tags - existing_tag_names
        if new_tags:
            if "tags" not in self.spec:
                self.spec["tags"] = []
            
            tag_descriptions = {
                "Earnings Calendar": "Endpoints for earnings calendar data",
                "User Queries": "Endpoints for user query management",
                "Shared Queries": "Endpoints for sharing queries",
                "Models": "Endpoints for model information"
            }
            
            for tag_name in new_tags:
                self.spec["tags"].append({
                    "name": tag_name,
                    "description": tag_descriptions.get(tag_name, f"Endpoints for {tag_name.lower()}")
                })
                print(f"  🏷️  Added tag: {tag_name}")
        
        return True
    
    def save_updated_spec(self) -> bool:
        """Save the updated OpenAPI specification."""
        try:
            # Create backup
            backup_file = self.openapi_file.with_suffix('.yaml.backup')
            with open(self.openapi_file, 'r', encoding='utf-8') as f:
                backup_content = f.read()
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(backup_content)
            print(f"💾 Created backup: {backup_file}")
            
            # Save updated spec
            with open(self.openapi_file, 'w', encoding='utf-8') as f:
                f.write(f"# Updated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Auto-updated OpenAPI specification\n\n")
                yaml.dump(self.spec, f, default_flow_style=False, sort_keys=False, indent=2)
            
            print(f"✅ Updated OpenAPI specification saved to {self.openapi_file}")
            return True
            
        except Exception as e:
            print(f"❌ Error saving updated specification: {e}")
            return False
    
    def auto_update(self) -> bool:
        """Main auto-update function."""
        print("🚀 Starting OpenAPI auto-update...")
        
        # Load existing specification
        if not self.load_existing_spec():
            return False
        
        # Discover endpoints in codebase
        self.discover_all_endpoints()
        
        # Find missing endpoints
        missing_endpoints = self.find_missing_endpoints()
        
        if not missing_endpoints:
            print("✅ OpenAPI specification is already up to date!")
            return True
        
        print(f"\n❌ Found {len(missing_endpoints)} missing endpoints:")
        for endpoint_key in missing_endpoints:
            print(f"  • {endpoint_key}")
        
        # Ask for confirmation
        if len(sys.argv) > 1 and sys.argv[1] == "--auto":
            confirm = "y"
        else:
            confirm = input(f"\n🤔 Do you want to add these {len(missing_endpoints)} endpoints to the OpenAPI spec? (y/n): ").lower()
        
        if confirm != 'y':
            print("❌ Update cancelled by user.")
            return False
        
        # Update the specification
        if not self.update_openapi_spec(missing_endpoints):
            return False
        
        # Save the updated specification
        if not self.save_updated_spec():
            return False
        
        print("🎉 OpenAPI specification auto-update completed successfully!")
        return True

def main():
    """Main function."""
    updater = OpenAPIAutoUpdater()
    success = updater.auto_update()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
